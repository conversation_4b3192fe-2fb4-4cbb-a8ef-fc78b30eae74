/* Security Settings Content */
.security-settings-content {
  position: relative;
  z-index: 2;
}

/* Additional gradient blur under body */
.security-settings-content::before {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  left: 0px;
  top: 400px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

.security-settings-content::after {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  right: 0px;
  top: 600px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

/* User Header */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 60px 40px 40px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Desktop: Position controls outside greeting */
@media (min-width: 769px) {
  .user-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .user-greeting {
    flex: 1;
    position: relative;
  }

  .header-controls {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }
}

/* User Header Background - Override general layout */
.security-settings-container .user-header::before {
  content: '';
  position: absolute;
  top: -120px;
  left: -50vw;
  right: -50vw;
  height: 360px !important; /* Calculated to cover greeting + controls */
  background: #260D08;
  z-index: -1;
}

/* Greeting Text */
.greeting-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.greeting-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 400;
  color: #CCCCCC;
  margin: 0 0 32px 0;
  line-height: 1.4;
}

/* Header Controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.stake-esvc-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-esvc-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

.btn-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

/* Balance Toggle */
.balance-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #CCCCCC;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #404040;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: #FFFFFF;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #BF4129;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
  margin-top: 10px; /* Reduced desktop spacing between red section and navbar */
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

/* Section Styling */
.section-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 32px 0;
  line-height: 1.2;
}

/* Security Main Content */
.security-main-content {
  padding: 0;
}

.security-options {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.security-option-card {
  display: flex;
  align-items: center;
  gap: 20px;
  background: #1A1A1A;
  border: 1px solid #333333;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.security-option-card:hover {
  background: #262626;
  border-color: #BF4129;
  transform: translateY(-2px);
}

.option-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #333333;
  border-radius: 12px;
  flex-shrink: 0;
}

.option-icon img,
.option-icon svg {
  width: 24px;
  height: 24px;
  object-fit: contain;
  color: #CCCCCC;
}

.option-content {
  flex: 1;
}

.option-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.option-description {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #CCCCCC;
  margin: 0;
  line-height: 1.4;
}

.option-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.option-arrow img {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

/* Security Form Content - Card Layout */
.security-form-content {
  min-height: 100vh;
  position: relative;
  z-index: 10;
  padding: 10px 24px 40px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.security-form-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  gap: 32px;
  position: absolute;
  width: 560px;
  height: auto;
  min-height: 480px;
  left: calc(50% - 560px/2);
  top: 80px;
  background: #262626;
  border-radius: 24px;
}

.form-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
  max-width: 480px;
  width: 100%;
}

.back-button {
  position: absolute;
  top: 30px;
  left: 24px;
  background: none;
  border: none;
  color: #D19049;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.3s ease;
}

.back-button:hover {
  color: #BF4129;
}

.back-button img,
.back-button svg {
  width: 16px;
  height: 16px;
  object-fit: contain;
  color: inherit;
}

.form-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 36px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  text-align: center;
}

.form-description {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 500;
  color: #CCCCCC;
  margin: 0;
  line-height: 1.6;
  text-align: center;
}

/* Security Form */
.security-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.form-group {
  width: 100%;
  text-align: left;
}

.form-label {
  display: block;
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  height: 56px;
  background: #404040;
  border: 1px solid #525252;
  border-radius: 12px;
  padding: 0 20px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

/* Password inputs need extra right padding for toggle button */
.input-wrapper .form-input[type="password"],
.input-wrapper .form-input[type="text"] {
  padding-right: 48px;
}

.form-input:focus {
  outline: none;
  border-color: #BF4129;
  background: #4A4A4A;
}

.form-input::placeholder {
  color: #999999;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  width: 16px;
  height: 16px;
  color: #888888;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.password-toggle:hover {
  color: #CCCCCC;
}

.password-toggle img,
.password-toggle svg {
  width: 16px;
  height: 16px;
  object-fit: contain;
  color: inherit;
}

.submit-button {
  width: 100%;
  height: 56px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-button:hover:not(:disabled) {
  background: #D19049;
  transform: translateY(-2px);
}

.submit-button:disabled {
  background: #666666;
  cursor: not-allowed;
  transform: none;
}

/* Password Requirements */
.password-requirements {
  background: #1A1A1A;
  border: 1px solid #333333;
  border-radius: 12px;
  padding: 20px;
  margin: 16px 0;
}

.requirements-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 12px 0;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirements-list li {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #888888;
  position: relative;
  padding-left: 24px;
}

.requirements-list li::before {
  content: '○';
  position: absolute;
  left: 0;
  color: #888888;
}

.requirements-list li.valid {
  color: #4CAF50;
}

.requirements-list li.valid::before {
  content: '✓';
  color: #4CAF50;
}

/* Verification Form */
.verify-email-content {
  max-width: 500px;
  margin: 0 auto;
  text-align: center;
}

.verification-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  width: 100%;
}

.verification-code {
  display: flex;
  gap: 12px;
  justify-content: center;
  width: 100%;
}

.code-inputs {
  display: flex;
  gap: 12px;
  justify-content: center;
  width: 100%;
}

.code-input {
  width: 56px;
  height: 56px;
  background: #404040;
  border: 1px solid #525252;
  border-radius: 12px;
  text-align: center;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.code-input:focus {
  outline: none;
  border-color: #BF4129;
  background: #4A4A4A;
}

.code-input.invalid {
  border-color: #FF4444;
  background: rgba(255, 68, 68, 0.1);
}

/* Resend Section */
.resend-section {
  text-align: center;
  width: 100%;
}

.resend-section p,
.resend-text {
  color: #CCCCCC;
  font-size: 14px;
  margin: 0 0 8px 0;
  font-family: 'Montserrat', sans-serif;
  text-align: center;
}

.resend-link {
  background: none;
  border: none;
  color: #BF4129;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

.resend-link:hover:not(:disabled) {
  color: #D19049;
}

.resend-link:disabled {
  color: #666666;
  cursor: not-allowed;
  text-decoration: none;
}

.change-email-btn {
  background: none;
  border: none;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  margin-top: 24px;
  transition: color 0.3s ease;
}

.change-email-btn:hover {
  color: #FFFFFF;
}

.change-email-btn img,
.change-email-btn svg {
  width: 14px;
  height: 14px;
  object-fit: contain;
  color: inherit;
}

/* Success Content - Card Layout */
.success-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 8px;
}

.success-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon img {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

.success-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
  text-align: center;
}

.success-description {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #CCCCCC;
  margin: 0;
  text-align: center;
  line-height: 1.5;
}

.success-button {
  background: #BF4129;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.success-button:hover {
  background: #A63622;
  transform: translateY(-2px);
}

/* Reset Password Content */
.reset-password-content {
  max-width: 500px;
  margin: 0 auto;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  /* Mobile Header Background */
  .security-settings-container .user-header::before {
    height: 385px !important;
  }

  .user-header {
    padding: 40px 20px 20px;
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .greeting-text {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .greeting-subtitle {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .header-controls {
    width: 100%;
    justify-content: space-between;
    gap: 16px;
  }

  .stake-esvc-btn {
    padding: 12px 16px;
    font-size: 14px;
  }

  .balance-toggle {
    gap: 8px;
  }

  .toggle-label {
    font-size: 12px;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px;
    gap: 24px;
    margin-top: -100px !important;
  }

  .dashboard-content {
    max-width: 100%;
  }

  .section-title {
    font-size: 24px;
    margin-bottom: 24px;
  }

  .security-option-card {
    padding: 20px;
    gap: 16px;
  }

  .option-icon {
    width: 40px;
    height: 40px;
  }

  .option-icon img {
    width: 20px;
    height: 20px;
  }

  .option-title {
    font-size: 18px;
  }

  .option-description {
    font-size: 13px;
  }

  /* Security Form Card - Mobile */
  .security-form-content {
    padding: 10px 16px 40px;
  }

  .security-form-card {
    width: calc(100vw - 32px);
    max-width: 400px;
    left: 50%;
    transform: translateX(-50%);
    padding: 24px;
    gap: 24px;
    position: relative;
    top: 50px;
  }

  .form-title {
    font-size: 28px;
    line-height: 34px;
  }

  .form-description {
    font-size: 16px;
    line-height: 22px;
  }

  .security-form {
    max-width: 100%;
  }

  .form-input {
    height: 48px;
    padding: 0 48px 0 16px;
    font-size: 14px;
  }

  .input-wrapper .form-input[type="password"],
  .input-wrapper .form-input[type="text"] {
    padding-right: 40px;
  }

  .password-toggle {
    right: 12px;
    width: 14px;
    height: 14px;
  }

  .password-toggle img,
  .password-toggle svg {
    width: 14px;
    height: 14px;
  }

  .submit-button {
    height: 48px;
    font-size: 14px;
  }

  .code-inputs {
    gap: 8px;
  }

  .code-input {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .success-icon {
    width: 64px;
    height: 64px;
  }

  .success-modal-content {
    padding: 32px 24px;
    margin: 20px;
  }

  .success-icon img {
    width: 48px;
    height: 48px;
  }

  .success-title {
    font-size: 20px;
  }

  .success-description {
    font-size: 14px;
  }

  .success-button {
    padding: 14px 24px;
    font-size: 14px;
  }

  .back-button {
    top: 20px;
    left: 16px;
    font-size: 14px;
    gap: 6px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    min-height: 44px;
    padding: 8px;
  }

  .back-button img,
  .back-button svg {
    width: 14px;
    height: 14px;
  }

  .password-requirements {
    padding: 16px;
  }

  .requirements-title {
    font-size: 13px;
  }

  .requirements-list li {
    font-size: 13px;
  }
}
