import React, { useState } from 'react';
import '../styles/components/TradeChallenge.css';
import DashboardLayout from './DashboardLayout';

// Import icons
import spanImage from '../assets/span.png';
import trendUpIcon from '../assets/trend-up.png';
import informationCircleIcon from '../assets/information-circle.png';

interface TradeChallengeProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const TradeChallenge: React.FC<TradeChallengeProps> = () => {

  return (
    <DashboardLayout className="trade-challenge-container">
      <div className="trade-challenge-content">
        {/* Hero Section */}
        <div className="hero-section">
          <h1 className="hero-title">
            <span className="amount">$1,000</span>
            <span className="arrow">→</span>
            <span className="target">1 Billion in 3,000 Trades</span>
          </h1>
          <p className="hero-subtitle">
            Join fully automated trading powered by our flagship from trading bot. Connect your
            exchange via a one-time entry fee, and let our bot back on your behalf. Start trading
            automatic. We only win when you do.
          </p>
          <button className="get-bot-btn">Get the BOT - $300/Year</button>
        </div>

        {/* Main Content */}
        <div className="main-content">
          <div className="content-header">
            <h2 className="content-title">No hype. Just discipline, Bitcoin and a BOT.</h2>
            <p className="content-subtitle">
              Watch Chief Gains Chikueze take on the ultimate 1,000-trade compounding challenge.
              Tracked live and funded by ESVC Capital.
            </p>
          </div>

          {/* Trading Dashboard */}
          <div className="trading-dashboard">
            {/* Live Tracker */}
            <div className="live-tracker">
              <h3 className="tracker-title">Live Tracker</h3>
              <div className="tracker-stats">
                <div className="stat-item">
                  <div className="stat-icon">💰</div>
                  <div className="stat-info">
                    <span className="stat-label">INITIAL STARTING CAPITAL</span>
                    <span className="stat-value">$1,000</span>
                  </div>
                </div>
                <div className="stat-item">
                  <div className="stat-icon">☁️</div>
                  <div className="stat-info">
                    <span className="stat-label">CURRENT BALANCE</span>
                    <span className="stat-value">$2,166,500</span>
                  </div>
                </div>
                <div className="stat-item">
                  <div className="stat-icon">🎯</div>
                  <div className="stat-info">
                    <span className="stat-label">TRADE COUNT</span>
                    <span className="stat-value">114 / 1000</span>
                  </div>
                </div>
                <div className="stat-item">
                  <div className="stat-icon">📈</div>
                  <div className="stat-info">
                    <span className="stat-label">ROI</span>
                    <span className="stat-value">+4.3%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Live Balance Trend Chart */}
            <div className="balance-trend">
              <h3 className="chart-title">Live Balance Trend</h3>
              <div className="chart-placeholder">
                {/* Chart will be implemented here */}
                <div className="chart-area">Chart visualization goes here</div>
              </div>
            </div>
          </div>

          {/* Trading Options */}
          <div className="trading-options">
            <button className="option-btn active">
              <span className="option-icon">🔄</span>
              Auto-trading logic
            </button>
            <button className="option-btn">
              <span className="option-icon">⚙️</span>
              Custom rules by Gains
            </button>
            <button className="option-btn">
              <span className="option-icon">🏗️</span>
              Built for long-term compounding
            </button>
          </div>

          {/* Bottom CTA */}
          <div className="bottom-cta">
            <button className="get-bot-btn-bottom">Get the BOT - $300/Year</button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default TradeChallenge;
