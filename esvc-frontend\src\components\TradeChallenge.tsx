import React, { useState } from 'react';
import '../styles/components/TradeChallenge.css';
import DashboardLayout from './DashboardLayout';
import SideNav from './SideNav';

// Import icons
import spanImage from '../assets/span.png';
import trendUpIcon from '../assets/trend-up.png';
import informationCircleIcon from '../assets/information-circle.png';

interface TradeChallengeProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const TradeChallenge: React.FC<TradeChallengeProps> = () => {
  const [activeTab, setActiveTab] = useState('trade-challenge');

  const challengeCards = [
    {
      title: 'ACTIVE TRADERS',
      value: '2,847',
      change: '+12.3% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'TOTAL PRIZE POOL',
      value: '$500,000',
      change: '+5.2% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'CURRENT LEADER PROFIT',
      value: '$47,892',
      change: '+18.7% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon,
      titleIcon: informationCircleIcon
    },
    {
      title: 'DAYS REMAINING',
      value: '23',
      change: 'Challenge Ends Soon',
      changeType: 'neutral',
      changeIcon: informationCircleIcon
    },
    {
      title: 'MINIMUM ENTRY',
      value: '$1,000',
      change: 'Start Trading Now',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'TOP 100 WINNERS',
      value: '100',
      change: 'Guaranteed Prizes',
      changeType: 'positive',
      changeIcon: trendUpIcon
    }
  ];

  const leaderboardData = [
    { rank: 1, trader: 'CryptoKing2024', profit: '$47,892', roi: '+478.92%' },
    { rank: 2, trader: 'DiamondHands', profit: '$43,156', roi: '+431.56%' },
    { rank: 3, trader: 'MoonTrader', profit: '$38,947', roi: '+389.47%' },
    { rank: 4, trader: 'BullRun2024', profit: '$35,821', roi: '+358.21%' },
    { rank: 5, trader: 'HODLMaster', profit: '$32,654', roi: '+326.54%' }
  ];

  return (
    <DashboardLayout className="trade-challenge-container">
      <div className="trade-challenge-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Trade
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Challenge
          </h1>
          <p className="page-subtitle">
            Join our $500,000 trading challenge. Compete with traders worldwide and win big prizes.
            Turn $1,000 into life-changing profits.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <SideNav
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="challenge-header">
              <h2 className="section-title">Challenge Overview</h2>
              <button className="join-challenge-btn">Join Challenge - $1,000</button>
            </div>

            <div className="dashboard-cards">
              {challengeCards.map((card, index) => (
                <div key={index} className="dashboard-card">
                  <div className="card-header">
                    <h3 className="card-title">
                      {card.title}
                      {card.titleIcon && <img src={card.titleIcon} alt="Title icon" className="title-icon" />}
                    </h3>
                  </div>
                  <div className="card-content">
                    <div className="card-value">
                      {card.value}
                    </div>
                    <div className={`card-change ${card.changeType}`}>
                      <img src={card.changeIcon} alt="Change indicator" className="change-icon" />
                      {card.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Leaderboard Section */}
            <div className="leaderboard-section">
              <h3 className="leaderboard-title">Live Leaderboard</h3>
              <div className="leaderboard-table">
                <div className="leaderboard-header">
                  <span>Rank</span>
                  <span>Trader</span>
                  <span>Profit</span>
                  <span>ROI</span>
                </div>
                {leaderboardData.map((trader) => (
                  <div key={trader.rank} className="leaderboard-row">
                    <span className="rank">#{trader.rank}</span>
                    <span className="trader-name">{trader.trader}</span>
                    <span className="profit">{trader.profit}</span>
                    <span className="roi">{trader.roi}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default TradeChallenge;
