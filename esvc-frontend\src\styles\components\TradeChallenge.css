/* Trade Challenge Content */
.trade-challenge-content {
  position: relative;
  z-index: 2;
}

/* Page Header */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 64px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
}

.title-with-span {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.title-span {
  max-width: 300px;
  height: auto;
}

.page-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  min-width: 0;
}

.challenge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.join-challenge-btn {
  background: #BF4129;
  color: #FFFFFF;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.join-challenge-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

/* Dashboard Cards */
.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

.dashboard-card {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.2);
}

.card-header {
  margin-bottom: 16px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #CCCCCC;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.title-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 1.2;
}

.card-change {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.card-change.positive {
  color: #10B981;
}

.card-change.neutral {
  color: #F59E0B;
}

.change-icon {
  width: 16px;
  height: 16px;
}

/* Leaderboard Section */
.leaderboard-section {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  margin-top: 32px;
}

.leaderboard-title {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 24px 0;
}

.leaderboard-table {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.leaderboard-header {
  display: grid;
  grid-template-columns: 80px 1fr 120px 120px;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
  font-weight: 600;
  color: #CCCCCC;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.leaderboard-row {
  display: grid;
  grid-template-columns: 80px 1fr 120px 120px;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.leaderboard-row:hover {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
}

.rank {
  font-weight: 700;
  color: #BF4129;
}

.trader-name {
  font-weight: 600;
  color: #FFFFFF;
}

.profit {
  font-weight: 600;
  color: #10B981;
}

.roi {
  font-weight: 600;
  color: #10B981;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 40px 20px 30px;
  }

  .page-title {
    font-size: 42px;
    gap: 12px;
    justify-content: center;
  }

  .title-with-span {
    align-items: center;
  }

  .title-span {
    max-width: 180px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px;
    gap: 24px;
  }

  .challenge-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .join-challenge-btn {
    width: 100%;
    text-align: center;
  }

  .dashboard-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .leaderboard-section {
    padding: 20px;
  }

  .leaderboard-header,
  .leaderboard-row {
    grid-template-columns: 60px 1fr 100px 100px;
    gap: 8px;
    font-size: 12px;
  }

  .leaderboard-header {
    font-size: 11px;
  }
}
